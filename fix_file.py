#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复main_module.py文件
"""

def fix_main_module():
    """修复main_module.py文件，删除重复的样式表代码"""
    try:
        # 读取文件
        with open('main_module.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"原文件有 {len(lines)} 行")
        
        # 找到第一个重复样式表的开始位置
        # 我们保留到第10351行（包含）
        keep_lines = lines[:10351]
        
        print(f"保留前 {len(keep_lines)} 行")
        
        # 写回文件
        with open('main_module.py', 'w', encoding='utf-8') as f:
            f.writelines(keep_lines)
        
        print("文件修复完成！")
        
    except Exception as e:
        print(f"修复文件时出错: {e}")

if __name__ == "__main__":
    fix_main_module()
