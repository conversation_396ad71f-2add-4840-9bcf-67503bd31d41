#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量注释调试日志的脚本 - 更全面版本
"""

import re
import os

def batch_comment_debug_logs(file_path):
    """批量注释掉调试日志"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    modified_lines = []
    
    for line in lines:
        # 检查是否是需要注释的print语句
        stripped = line.strip()
        
        # 跳过已经被注释的行
        if stripped.startswith('# print('):
            modified_lines.append(line)
            continue
            
        # 需要注释的调试日志模式
        should_comment = False
        
        # 检查各种调试日志模式
        debug_patterns = [
            r'print\(f?"智能加减速功能已',
            r'print\(f?"速度监控定时器',
            r'print\(f?"模糊控制定时器',
            r'print\(f?"颜色控制定时器',
            r'print\(f?"变换.*定时器',
            r'print\(f?"视频和音频媒体源下拉框已更新',
            r'print\(f?".*下拉框已更新',
            r'print\(f?".*已更新',
            r'print\(f?".*已启动',
            r'print\(f?".*已停止',
            r'print\(f?".*已禁用',
            r'print\(f?".*已启用',
            r'print\(f?".*已连接',
            r'print\(f?".*已断开',
            r'print\(f?".*已创建',
            r'print\(f?".*已加载',
            r'print\(f?".*已保存',
            r'print\(f?".*已完成',
            r'print\(f?".*成功',
            r'print\(f?".*完成',
            r'print\(f?".*开始',
            r'print\(f?".*正在',
            r'print\(f?".*准备',
            r'print\(f?".*尝试',
            r'print\(f?".*检测',
            r'print\(f?".*获取',
            r'print\(f?".*设置',
            r'print\(f?".*应用',
            r'print\(f?".*初始化',
            r'print\(f?".*连接',
            r'print\(f?".*启动',
            r'print\(f?".*停止',
            r'print\(f?".*刷新',
            r'print\(f?".*更新',
            r'print\(f?".*加载',
            r'print\(f?".*保存',
            r'print\(f?".*创建',
            r'print\(f?".*生成',
            r'print\(f?".*发送',
            r'print\(f?".*接收',
            r'print\(f?".*收到',
            r'print\(f?".*处理',
            r'print\(f?".*执行',
            r'print\(f?".*切换',
            r'print\(f?".*选择',
            r'print\(f?".*添加',
            r'print\(f?".*删除',
            r'print\(f?".*清空',
            r'print\(f?".*重置',
            r'print\(f?".*恢复',
            r'print\(f?".*修改',
            r'print\(f?".*变更',
            r'print\(f?".*调整',
            r'print\(f?".*配置',
            r'print\(f?".*设定',
            r'print\(f?".*建立',
            r'print\(f?".*关闭',
            r'print\(f?".*结束',
            r'print\(f?".*退出',
            r'print\(f?".*进入',
            r'print\(f?".*离开',
            r'print\(f?".*返回',
            r'print\(f?".*跳过',
            r'print\(f?".*忽略',
            r'print\(f?".*继续',
            r'print\(f?".*暂停',
            r'print\(f?".*恢复',
            r'print\(f?".*重试',
            r'print\(f?".*等待',
            r'print\(f?".*监听',
            r'print\(f?".*监控',
            r'print\(f?".*检查',
            r'print\(f?".*验证',
            r'print\(f?".*确认',
            r'print\(f?".*测试',
            r'print\(f?".*调试',
            r'print\(f?".*日志',
            r'print\(f?".*信息',
            r'print\(f?".*状态',
            r'print\(f?".*结果',
            r'print\(f?".*数据',
            r'print\(f?".*参数',
            r'print\(f?".*配置',
            r'print\(f?".*选项',
            r'print\(f?".*设备',
            r'print\(f?".*文件',
            r'print\(f?".*路径',
            r'print\(f?".*目录',
            r'print\(f?".*窗口',
            r'print\(f?".*界面',
            r'print\(f?".*组件',
            r'print\(f?".*控件',
            r'print\(f?".*按钮',
            r'print\(f?".*标签',
            r'print\(f?".*输入',
            r'print\(f?".*输出',
            r'print\(f?".*播放',
            r'print\(f?".*停止',
            r'print\(f?".*暂停',
            r'print\(f?".*继续',
            r'print\(f?".*音频',
            r'print\(f?".*视频',
            r'print\(f?".*媒体',
            r'print\(f?".*源',
            r'print\(f?".*流',
            r'print\(f?".*数据流',
            r'print\(f?".*信号',
            r'print\(f?".*事件',
            r'print\(f?".*消息',
            r'print\(f?".*通知',
            r'print\(f?".*提示',
            r'print\(f?".*警告',
            r'print\(f?".*错误',
            r'print\(f?".*异常',
            r'print\(f?".*失败',
            r'print\(f?".*成功',
            r'print\(f?".*完成',
            r'print\(f?".*结束',
        ]
        
        # 检查是否匹配任何调试模式
        for pattern in debug_patterns:
            if re.search(pattern, stripped):
                should_comment = True
                break
        
        # 如果需要注释，添加注释符号
        if should_comment and stripped.startswith('print('):
            # 保持原有的缩进
            indent = line[:len(line) - len(line.lstrip())]
            commented_line = indent + '# ' + line.lstrip()
            modified_lines.append(commented_line)
        else:
            modified_lines.append(line)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(modified_lines)
    
    print(f"已处理文件: {file_path}")

if __name__ == "__main__":
    # 处理main_module.py文件
    file_path = "main_module.py"
    if os.path.exists(file_path):
        batch_comment_debug_logs(file_path)
        print("批量注释调试日志完成！")
    else:
        print(f"文件不存在: {file_path}")
