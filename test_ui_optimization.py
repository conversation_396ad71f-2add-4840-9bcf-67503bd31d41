#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI优化测试脚本
测试现代化UI设计的效果
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTabWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def test_modern_ui():
    """测试现代化UI设计"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = QMainWindow()
    window.setWindowTitle("🎬 OBS 智能去重软件 v1.3 - UI优化测试")
    window.setGeometry(100, 30, 1400, 1000)
    window.setMinimumSize(1400, 1000)
    
    # 设置现代化样式
    window.setStyleSheet("""
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', '<PERSON><PERSON><PERSON> UI', sans-serif;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #f1f5f9, stop:1 #ffffff);
            color: #1e293b;
        }
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:0.5 #764ba2, stop:1 #667eea);
        }
        /* 现代化滚动条样式 */
        QScrollBar:vertical {
            background: #f1f5f9;
            width: 12px;
            border-radius: 6px;
            margin: 0px;
        }
        QScrollBar::handle:vertical {
            background: #cbd5e1;
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }
        QScrollBar::handle:vertical:hover {
            background: #94a3b8;
        }
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
        /* 现代化分组框样式 */
        QGroupBox {
            font-weight: bold;
            font-size: 12pt;
            color: #1e293b;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            margin-top: 15px;
            padding-top: 15px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ffffff, stop:1 #f8fafc);
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            color: #374151;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ffffff, stop:1 #f8fafc);
            border-radius: 6px;
        }
    """)
    
    # 创建中央部件
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    # 主布局
    main_layout = QVBoxLayout(central_widget)
    main_layout.setContentsMargins(20, 20, 20, 20)
    main_layout.setSpacing(20)
    
    # 顶部连接区域
    connection_layout = QHBoxLayout()
    connection_layout.setSpacing(20)
    connection_layout.setContentsMargins(20, 15, 20, 15)
    
    # 状态标签容器
    status_container = QWidget()
    status_container.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #fee2e2, stop:1 #fecaca);
            border: 2px solid #f87171;
            border-radius: 15px;
            padding: 5px;
        }
    """)
    status_layout = QVBoxLayout(status_container)
    status_layout.setContentsMargins(15, 10, 15, 10)
    
    status_label = QLabel('🔴 未连接')
    status_label.setAlignment(Qt.AlignCenter)
    status_label.setStyleSheet("""
        QLabel {
            background: transparent;
            color: #dc2626;
            font-weight: bold;
            font-size: 14pt;
            min-height: 25px;
            border: none;
        }
    """)
    
    status_subtitle = QLabel('等待连接到 OBS Studio')
    status_subtitle.setAlignment(Qt.AlignCenter)
    status_subtitle.setStyleSheet("""
        QLabel {
            background: transparent;
            color: #b91c1c;
            font-size: 10pt;
            border: none;
            margin-top: 2px;
        }
    """)
    
    status_layout.addWidget(status_label)
    status_layout.addWidget(status_subtitle)
    
    # 连接按钮容器
    connect_container = QWidget()
    connect_container.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #1d4ed8);
            border: 2px solid #2563eb;
            border-radius: 15px;
            padding: 5px;
        }
    """)
    connect_layout = QVBoxLayout(connect_container)
    connect_layout.setContentsMargins(15, 10, 15, 10)
    
    connect_button = QPushButton('🔗 连接到 OBS')
    connect_button.setStyleSheet("""
        QPushButton {
            background: transparent;
            color: white;
            border: none;
            padding: 8px 16px;
            font-weight: bold;
            font-size: 12pt;
            min-width: 140px;
            min-height: 25px;
        }
        QPushButton:hover {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        QPushButton:pressed {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
    """)
    
    connect_subtitle = QPushButton('建立 WebSocket 连接')
    connect_subtitle.setStyleSheet("""
        QPushButton {
            background: transparent;
            color: #bfdbfe;
            border: none;
            padding: 2px 16px;
            font-size: 9pt;
            min-height: 15px;
        }
        QPushButton:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }
    """)
    connect_subtitle.setEnabled(False)
    
    connect_layout.addWidget(connect_button)
    connect_layout.addWidget(connect_subtitle)
    
    # 一键启动按钮容器
    quick_start_container = QWidget()
    quick_start_container.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #10b981, stop:1 #059669);
            border: 2px solid #059669;
            border-radius: 15px;
            padding: 5px;
        }
    """)
    quick_start_layout = QVBoxLayout(quick_start_container)
    quick_start_layout.setContentsMargins(15, 10, 15, 10)
    
    quick_start_button = QPushButton('🚀 一键启动')
    quick_start_button.setStyleSheet("""
        QPushButton {
            background: transparent;
            color: white;
            border: none;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 120px;
            font-size: 12pt;
            min-height: 25px;
        }
        QPushButton:hover {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        QPushButton:pressed {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
    """)
    
    quick_start_subtitle = QPushButton('批量启动选定功能')
    quick_start_subtitle.setStyleSheet("""
        QPushButton {
            background: transparent;
            color: #a7f3d0;
            border: none;
            padding: 2px 16px;
            font-size: 9pt;
            min-height: 15px;
        }
        QPushButton:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }
    """)
    quick_start_subtitle.setEnabled(False)
    
    quick_start_layout.addWidget(quick_start_button)
    quick_start_layout.addWidget(quick_start_subtitle)
    
    connection_layout.addWidget(status_container, 1)
    connection_layout.addWidget(connect_container)
    connection_layout.addWidget(quick_start_container)
    
    main_layout.addLayout(connection_layout)
    
    # 创建Tab控件
    tab_widget = QTabWidget()
    tab_widget.setStyleSheet("""
        QTabWidget::pane {
            border: 3px solid #e2e8f0;
            border-radius: 18px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #f1f5f9);
            margin-top: 8px;
            padding: 5px;
        }
        QTabBar::tab {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f8fafc, stop:1 #e2e8f0);
            color: #64748b;
            padding: 15px 25px;
            margin-right: 5px;
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            font-weight: bold;
            font-size: 12pt;
            min-width: 120px;
            border: 2px solid #cbd5e1;
            border-bottom: none;
            position: relative;
        }
        QTabBar::tab:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:0.5 #764ba2, stop:1 #667eea);
            color: white;
            border-color: #667eea;
            margin-bottom: -3px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e2e8f0, stop:1 #cbd5e1);
            border-color: #94a3b8;
        }
    """)
    
    # 创建测试Tab页
    test_tab = QWidget()
    test_layout = QVBoxLayout(test_tab)
    test_layout.setContentsMargins(20, 20, 20, 20)
    
    # 测试标题
    title_widget = QWidget()
    title_widget.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:0.5 #764ba2, stop:1 #667eea);
            border-radius: 20px;
            border: 3px solid #5a67d8;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
    """)
    title_layout = QVBoxLayout(title_widget)
    title_layout.setContentsMargins(25, 20, 25, 20)
    
    title_label = QLabel("🎨 UI现代化优化测试")
    title_label.setAlignment(Qt.AlignCenter)
    title_label.setStyleSheet("""
        QLabel {
            color: white;
            font-size: 20pt;
            font-weight: bold;
            background: transparent;
            border: none;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
    """)
    
    subtitle_label = QLabel("✨ 现代化设计风格展示")
    subtitle_label.setAlignment(Qt.AlignCenter)
    subtitle_label.setStyleSheet("""
        QLabel {
            color: #e2e8f0;
            font-size: 13pt;
            background: transparent;
            border: none;
            margin-top: 8px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
    """)
    
    title_layout.addWidget(title_label)
    title_layout.addWidget(subtitle_label)
    
    test_layout.addWidget(title_widget)
    
    # 添加一些测试内容
    content_widget = QWidget()
    content_widget.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #f1f5f9);
            border-radius: 20px;
            border: 3px solid #e2e8f0;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }
    """)
    content_layout = QVBoxLayout(content_widget)
    content_layout.setContentsMargins(25, 25, 25, 25)
    content_layout.setSpacing(20)
    
    info_label = QLabel("🎯 优化内容：")
    info_label.setStyleSheet("""
        QLabel {
            font-weight: bold;
            color: #1e293b;
            font-size: 14pt;
            background: transparent;
            border: none;
        }
    """)
    content_layout.addWidget(info_label)
    
    features = [
        "✅ 窗口尺寸优化：1400x1000，确保内容完全显示",
        "✅ 现代化渐变背景和圆角设计",
        "✅ 改进的按钮样式和悬停效果",
        "✅ 优化的Tab控件设计",
        "✅ 更好的颜色搭配和视觉层次",
        "✅ 改进的字体和间距",
        "✅ 现代化的滚动条样式",
        "✅ 优化的分组框设计"
    ]
    
    for feature in features:
        feature_label = QLabel(feature)
        feature_label.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 11pt;
                background: transparent;
                border: none;
                padding: 5px 0;
            }
        """)
        content_layout.addWidget(feature_label)
    
    test_layout.addWidget(content_widget)
    test_layout.addStretch()
    
    tab_widget.addTab(test_tab, "🎨 UI测试")
    
    main_layout.addWidget(tab_widget)
    
    window.show()
    
    print("🎨 UI现代化优化测试窗口已打开")
    print("📋 测试内容：")
    print("   - 窗口尺寸：1400x1000")
    print("   - 现代化渐变背景")
    print("   - 改进的按钮和Tab样式")
    print("   - 优化的布局和间距")
    
    return app.exec_()

if __name__ == "__main__":
    test_modern_ui() 