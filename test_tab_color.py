#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Tab选中时的字体颜色效果
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QTabWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def test_tab_colors():
    """测试Tab字体颜色"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    # 创建主窗口
    window = QWidget()
    window.setWindowTitle("Tab字体颜色测试")
    window.setGeometry(300, 200, 800, 600)
    
    # 设置整体窗口样式
    window.setStyleSheet("""
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #f8fafc, stop:0.5 #e2e8f0, stop:1 #f1f5f9);
        }
    """)
    
    layout = QVBoxLayout(window)
    layout.setSpacing(20)
    layout.setContentsMargins(20, 20, 20, 20)
    
    # 标题
    title = QLabel("Tab选中时字体颜色测试")
    title.setAlignment(Qt.AlignCenter)
    title.setStyleSheet("""
        QLabel {
            font-size: 18pt;
            font-weight: bold;
            color: #374151;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
        }
    """)
    layout.addWidget(title)
    
    # 创建Tab控件 - 使用修改后的样式
    tab_widget = QTabWidget()
    tab_widget.setStyleSheet("""
        QTabWidget::pane {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            margin-top: 5px;
        }
        QTabBar::tab {
            background: #f1f5f9;
            color: #64748b;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        QTabBar::tab:selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: black;
        }
        QTabBar::tab:hover {
            background: #e2e8f0;
            color: #374151;
        }
    """)
    
    # 添加Tab页面
    for i in range(4):
        tab = QWidget()
        tab_layout = QVBoxLayout(tab)
        
        content_label = QLabel(f"这是第 {i+1} 个Tab页面的内容")
        content_label.setAlignment(Qt.AlignCenter)
        content_label.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                color: #374151;
                padding: 20px;
                background: #f8fafc;
                border-radius: 8px;
                border: 1px solid #e2e8f0;
            }
        """)
        tab_layout.addWidget(content_label)
        
        # 添加说明文字
        info_label = QLabel("✨ 选中的Tab标签字体现在是黑色的！")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #059669;
                font-weight: bold;
                background: #dcfce7;
                padding: 10px;
                border-radius: 6px;
                border: 2px solid #22c55e;
                margin-top: 20px;
            }
        """)
        tab_layout.addWidget(info_label)
        
        tab_layout.addStretch()
        
        # 根据不同Tab设置不同的标题
        tab_titles = ["🎬 视频去重", "🎵 音频去重", "⚡ 爆闪播放器", "📖 说明书"]
        tab_widget.addTab(tab, tab_titles[i])
    
    layout.addWidget(tab_widget)
    
    # 添加说明
    instruction_label = QLabel("""
    🎯 测试说明：
    • 点击不同的Tab标签查看效果
    • 选中的Tab背景是渐变色，字体是黑色
    • 未选中的Tab是灰色背景，灰色字体
    • 鼠标悬停时会有高亮效果
    """)
    instruction_label.setWordWrap(True)
    instruction_label.setStyleSheet("""
        QLabel {
            color: #374151;
            font-size: 11pt;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            line-height: 1.5;
        }
    """)
    layout.addWidget(instruction_label)
    
    # 显示窗口
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_tab_colors()
