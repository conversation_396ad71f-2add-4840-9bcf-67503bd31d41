#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback

def test_import():
    try:
        print("开始导入main_module...")
        import main_module
        print("导入成功！")
        return True
    except Exception as e:
        print(f"导入失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_import()
    if success:
        print("测试通过")
        sys.exit(0)
    else:
        print("测试失败")
        sys.exit(1)
