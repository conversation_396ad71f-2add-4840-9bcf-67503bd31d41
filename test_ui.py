#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI测试脚本 - 用于测试升级后的UI效果
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTabWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def test_ui():
    """测试UI效果"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    # 创建主窗口
    window = QWidget()
    window.setWindowTitle("🎬 OBS 智能去重软件 v1.3 - UI测试")
    window.setGeometry(150, 50, 1200, 900)
    
    # 设置整体窗口样式
    window.setStyleSheet("""
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #f8fafc, stop:0.5 #e2e8f0, stop:1 #f1f5f9);
        }
    """)
    
    layout = QVBoxLayout(window)
    layout.setSpacing(20)
    layout.setContentsMargins(20, 20, 20, 20)
    
    # 标题
    title = QLabel("🎬 OBS 智能去重软件 UI 升级预览")
    title.setAlignment(Qt.AlignCenter)
    title.setStyleSheet("""
        QLabel {
            font-size: 24pt;
            font-weight: bold;
            color: white;
            padding: 20px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 15px;
            border: 3px solid #5a67d8;
        }
    """)
    layout.addWidget(title)
    
    # 连接状态示例
    status_connected = QLabel("🟢 已连接到 OBS")
    status_connected.setAlignment(Qt.AlignCenter)
    status_connected.setStyleSheet("""
        QLabel {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #dcfce7, stop:1 #bbf7d0);
            color: #15803d;
            border: 2px solid #22c55e;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: bold;
            font-size: 14pt;
            min-height: 20px;
        }
    """)
    layout.addWidget(status_connected)
    
    # 按钮示例
    button_layout = QVBoxLayout()
    
    # 连接按钮
    connect_btn = QPushButton("🔗 连接到 OBS")
    connect_btn.setStyleSheet("""
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #3b82f6, stop:1 #1d4ed8);
            color: white;
            border: 2px solid #2563eb;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: bold;
            font-size: 12pt;
            min-width: 140px;
        }
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #2563eb, stop:1 #1e40af);
            border-color: #1d4ed8;
            transform: translateY(-2px);
        }
    """)
    button_layout.addWidget(connect_btn)
    
    # 一键启动按钮
    quick_start_btn = QPushButton("🚀 一键启动")
    quick_start_btn.setStyleSheet("""
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #10b981, stop:1 #059669);
            color: white;
            border: 2px solid #059669;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: bold;
            min-width: 120px;
            font-size: 12pt;
        }
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #059669, stop:1 #047857);
            border: 2px solid #047857;
            transform: translateY(-2px);
        }
    """)
    button_layout.addWidget(quick_start_btn)
    
    layout.addLayout(button_layout)
    
    # Tab示例
    tab_widget = QTabWidget()
    tab_widget.setStyleSheet("""
        QTabWidget::pane {
            border: 3px solid #e2e8f0;
            border-radius: 15px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #ffffff, stop:1 #f8fafc);
            margin-top: 5px;
        }
        QTabBar::tab {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #f1f5f9, stop:1 #e2e8f0);
            color: #64748b;
            padding: 12px 20px;
            margin-right: 3px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            font-weight: bold;
            font-size: 11pt;
            min-width: 100px;
            border: 2px solid #cbd5e1;
            border-bottom: none;
        }
        QTabBar::tab:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
            color: white;
            border-color: #667eea;
            margin-bottom: -2px;
        }
        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #e2e8f0, stop:1 #cbd5e1);
            color: #374151;
        }
    """)
    
    # 添加Tab页面
    tab1 = QWidget()
    tab1_layout = QVBoxLayout(tab1)
    tab1_label = QLabel("🎬 智能视频去重系统")
    tab1_label.setAlignment(Qt.AlignCenter)
    tab1_label.setStyleSheet("""
        QLabel {
            font-size: 18pt;
            font-weight: bold;
            color: white;
            padding: 15px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 15px;
            border: 3px solid #5a67d8;
        }
    """)
    tab1_layout.addWidget(tab1_label)
    tab1_layout.addStretch()
    
    tab2 = QWidget()
    tab2_layout = QVBoxLayout(tab2)
    tab2_label = QLabel("🎵 智能音频去重系统")
    tab2_label.setAlignment(Qt.AlignCenter)
    tab2_label.setStyleSheet("""
        QLabel {
            font-size: 18pt;
            font-weight: bold;
            color: white;
            padding: 15px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #10b981, stop:1 #059669);
            border-radius: 15px;
            border: 3px solid #047857;
        }
    """)
    tab2_layout.addWidget(tab2_label)
    tab2_layout.addStretch()
    
    tab3 = QWidget()
    tab3_layout = QVBoxLayout(tab3)
    tab3_label = QLabel("⚡ 智能爆闪播放器")
    tab3_label.setAlignment(Qt.AlignCenter)
    tab3_label.setStyleSheet("""
        QLabel {
            color: white;
            font-size: 18pt;
            font-weight: bold;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 15px;
            border: 2px solid #5a67d8;
            padding: 20px;
        }
    """)
    tab3_layout.addWidget(tab3_label)
    tab3_layout.addStretch()
    
    tab_widget.addTab(tab1, "🎬 视频去重")
    tab_widget.addTab(tab2, "🎵 音频去重")
    tab_widget.addTab(tab3, "⚡ 爆闪播放器")
    
    layout.addWidget(tab_widget)
    
    # 显示窗口
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_ui()
