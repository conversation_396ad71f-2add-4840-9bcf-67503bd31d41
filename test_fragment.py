#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试碎片范围简化效果
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QCheckBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def test_fragment_ui():
    """测试简化后的碎片范围UI"""
    app = QApplication(sys.argv)
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    # 创建主窗口
    window = QWidget()
    window.setWindowTitle("碎片范围简化测试")
    window.setGeometry(300, 200, 600, 400)
    
    # 设置整体窗口样式
    window.setStyleSheet("""
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #f8fafc, stop:0.5 #e2e8f0, stop:1 #f1f5f9);
        }
    """)
    
    layout = QVBoxLayout(window)
    layout.setSpacing(20)
    layout.setContentsMargins(30, 30, 30, 30)
    
    # 标题
    title = QLabel("碎片范围设置对比")
    title.setAlignment(Qt.AlignCenter)
    title.setStyleSheet("""
        QLabel {
            font-size: 18pt;
            font-weight: bold;
            color: #374151;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
        }
    """)
    layout.addWidget(title)
    
    # 原版复杂样式（示例）
    old_label = QLabel("原版样式（复杂）:")
    old_label.setStyleSheet("font-weight: bold; color: #374151; font-size: 12pt;")
    layout.addWidget(old_label)
    
    old_widget = QWidget()
    old_widget.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #fef9c3, stop:1 #fef08a);
            border: 3px solid #eab308;
            border-radius: 12px;
            padding: 15px;
            margin: 2px;
        }
    """)
    old_layout = QVBoxLayout(old_widget)
    
    old_title = QLabel("📊 碎片数据范围")
    old_title.setAlignment(Qt.AlignCenter)
    old_title.setStyleSheet("""
        QLabel {
            font-weight: bold;
            color: white;
            font-size: 12pt;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #f59e0b, stop:1 #d97706);
            padding: 8px;
            border-radius: 10px;
            border: 2px solid #d97706;
        }
    """)
    old_layout.addWidget(old_title)
    layout.addWidget(old_widget)
    
    # 新版简化样式
    new_label = QLabel("新版样式（简化）:")
    new_label.setStyleSheet("font-weight: bold; color: #374151; font-size: 12pt;")
    layout.addWidget(new_label)
    
    # 简化的碎片范围设置
    fragment_simple_widget = QWidget()
    fragment_simple_widget.setStyleSheet("""
        QWidget {
            background: #fffbeb;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 8px;
            margin: 5px 0px;
        }
    """)
    fragment_simple_layout = QHBoxLayout(fragment_simple_widget)
    fragment_simple_layout.setSpacing(10)
    fragment_simple_layout.setContentsMargins(10, 8, 10, 8)

    # 简化标签
    fragment_label = QLabel("碎片范围:")
    fragment_label.setStyleSheet("""
        QLabel {
            font-weight: bold;
            color: #92400e;
            font-size: 11pt;
            background: transparent;
            border: none;
        }
    """)
    fragment_simple_layout.addWidget(fragment_label)

    # 最小值 - 简化样式
    fragment_min_spin = QSpinBox()
    fragment_min_spin.setRange(1, 1000)
    fragment_min_spin.setValue(50)
    fragment_min_spin.setStyleSheet("""
        QSpinBox {
            padding: 6px;
            border: 1px solid #d97706;
            border-radius: 4px;
            background: white;
            font-size: 10pt;
            min-width: 50px;
            color: #92400e;
        }
        QSpinBox:focus {
            border-color: #f59e0b;
        }
    """)
    fragment_simple_layout.addWidget(fragment_min_spin)

    # 连接符
    dash_simple = QLabel("~")
    dash_simple.setStyleSheet("""
        QLabel {
            font-weight: bold;
            color: #92400e;
            font-size: 11pt;
            background: transparent;
            border: none;
        }
    """)
    fragment_simple_layout.addWidget(dash_simple)

    # 最大值 - 简化样式
    fragment_max_spin = QSpinBox()
    fragment_max_spin.setRange(1, 1000)
    fragment_max_spin.setValue(100)
    fragment_max_spin.setStyleSheet(fragment_min_spin.styleSheet())
    fragment_simple_layout.addWidget(fragment_max_spin)

    # 单位
    unit_simple = QLabel("ms")
    unit_simple.setStyleSheet("""
        QLabel {
            color: #92400e;
            font-size: 10pt;
            background: transparent;
            border: none;
        }
    """)
    fragment_simple_layout.addWidget(unit_simple)
    fragment_simple_layout.addStretch()

    layout.addWidget(fragment_simple_widget)
    
    # 说明
    info_label = QLabel("✨ 新版本去掉了复杂的装饰，保留核心功能，界面更加简洁清爽！")
    info_label.setWordWrap(True)
    info_label.setAlignment(Qt.AlignCenter)
    info_label.setStyleSheet("""
        QLabel {
            color: #059669;
            font-size: 11pt;
            font-weight: bold;
            background: #dcfce7;
            padding: 10px;
            border-radius: 8px;
            border: 2px solid #22c55e;
        }
    """)
    layout.addWidget(info_label)
    
    layout.addStretch()
    
    # 显示窗口
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_fragment_ui()
