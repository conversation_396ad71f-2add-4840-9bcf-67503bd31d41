#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量注释调试日志的脚本
"""

import re
import os

def comment_debug_logs(file_path):
    """注释掉调试日志"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要注释的调试日志模式
    patterns_to_comment = [
        # 成功信息
        r'(\s*)print\(f?"✅[^"]*"\)',
        r'(\s*)print\(f?"✅[^"]*"[^)]*\)',
        # 信息提示
        r'(\s*)print\(f?"ℹ️[^"]*"\)',
        r'(\s*)print\(f?"ℹ️[^"]*"[^)]*\)',
        # 警告信息（保留一些重要的）
        r'(\s*)print\(f?"⚠️[^"]*音频处理库[^"]*"\)',
        r'(\s*)print\(f?"⚠️[^"]*设备[^"]*"\)',
        # 进度信息
        r'(\s*)print\(f?"🔄[^"]*"\)',
        r'(\s*)print\(f?"🔄[^"]*"[^)]*\)',
        # 其他调试信息
        r'(\s*)print\(f?"📁[^"]*"\)',
        r'(\s*)print\(f?"📊[^"]*"\)',
        r'(\s*)print\(f?"🎯[^"]*"\)',
        r'(\s*)print\(f?"🔊[^"]*"\)',
        r'(\s*)print\(f?"🎧[^"]*"\)',
        r'(\s*)print\(f?"🎚️[^"]*"\)',
        r'(\s*)print\(f?"🎵[^"]*"\)',
        # 一些特定的调试信息
        r'(\s*)print\(f?"正在[^"]*"\)',
        r'(\s*)print\(f?"成功[^"]*"\)',
        r'(\s*)print\(f?"已[^"]*"\)',
        r'(\s*)print\(f?"准备[^"]*"\)',
        r'(\s*)print\(f?"开始[^"]*"\)',
        r'(\s*)print\(f?"完成[^"]*"\)',
    ]
    
    # 逐个应用模式
    for pattern in patterns_to_comment:
        # 替换为注释版本
        content = re.sub(pattern, r'\1# print(', content, flags=re.MULTILINE)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已处理文件: {file_path}")

if __name__ == "__main__":
    # 处理main_module.py文件
    file_path = "main_module.py"
    if os.path.exists(file_path):
        comment_debug_logs(file_path)
        print("调试日志注释完成！")
    else:
        print(f"文件不存在: {file_path}")
