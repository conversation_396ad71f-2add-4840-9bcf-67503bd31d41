#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底清理main_module.py文件
"""

def clean_main_module():
    """彻底清理main_module.py文件，删除所有CSS残留代码"""
    try:
        # 读取文件
        with open('main_module.py', 'r', encoding='utf-8') as f:
            content = f.read()

        print(f"原文件大小: {len(content)} 字符")

        # 找到最后一个有效的Python代码位置
        # 查找 'print(f"鼠标事件处理出错: {e}")' 这行
        target_line = 'print(f"鼠标事件处理出错: {e}")'

        # 找到这行的位置
        pos = content.find(target_line)
        if pos == -1:
            print("找不到目标行！")
            return

        # 找到这行的结束位置
        end_pos = content.find('\n', pos)
        if end_pos == -1:
            end_pos = len(content)
        else:
            end_pos += 1  # 包含换行符

        # 截取到这个位置
        clean_content = content[:end_pos]

        print(f"清理后大小: {len(clean_content)} 字符")

        # 写回文件
        with open('main_module.py', 'w', encoding='utf-8') as f:
            f.write(clean_content)

        print("文件清理完成！")
        print("删除了所有CSS残留代码")

        # 验证文件
        with open('main_module.py', 'r', encoding='utf-8') as f:
            final_lines = f.readlines()

        print(f"最终文件有 {len(final_lines)} 行")

        # 显示最后几行
        print("\n最后5行内容:")
        for i, line in enumerate(final_lines[-5:], len(final_lines)-4):
            print(f"{i:5d}: {line.rstrip()}")

    except Exception as e:
        print(f"清理文件时出错: {e}")

if __name__ == "__main__":
    clean_main_module()
