import re

# 读取文件
with open('main_module.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 需要注释的print语句模式
patterns = [
    r'(\s+)print\(f?"[^"]*已启动[^"]*"\)',
    r'(\s+)print\(f?"[^"]*已停止[^"]*"\)',
    r'(\s+)print\(f?"[^"]*已启用[^"]*"\)',
    r'(\s+)print\(f?"[^"]*已禁用[^"]*"\)',
    r'(\s+)print\(f?"[^"]*已更新[^"]*"\)',
    r'(\s+)print\(f?"[^"]*已连接[^"]*"\)',
    r'(\s+)print\(f?"[^"]*已断开[^"]*"\)',
    r'(\s+)print\(f?"[^"]*成功[^"]*"\)',
    r'(\s+)print\(f?"[^"]*完成[^"]*"\)',
    r'(\s+)print\(f?"[^"]*开始[^"]*"\)',
    r'(\s+)print\(f?"[^"]*正在[^"]*"\)',
    r'(\s+)print\(f?"[^"]*准备[^"]*"\)',
    r'(\s+)print\(f?"[^"]*尝试[^"]*"\)',
    r'(\s+)print\(f?"[^"]*检测[^"]*"\)',
    r'(\s+)print\(f?"[^"]*获取[^"]*"\)',
    r'(\s+)print\(f?"[^"]*设置[^"]*"\)',
    r'(\s+)print\(f?"[^"]*应用[^"]*"\)',
    r'(\s+)print\(f?"[^"]*初始化[^"]*"\)',
    r'(\s+)print\(f?"[^"]*加载[^"]*"\)',
    r'(\s+)print\(f?"[^"]*保存[^"]*"\)',
    r'(\s+)print\(f?"[^"]*创建[^"]*"\)',
    r'(\s+)print\(f?"[^"]*生成[^"]*"\)',
    r'(\s+)print\(f?"[^"]*发送[^"]*"\)',
    r'(\s+)print\(f?"[^"]*接收[^"]*"\)',
    r'(\s+)print\(f?"[^"]*收到[^"]*"\)',
    r'(\s+)print\(f?"[^"]*处理[^"]*"\)',
    r'(\s+)print\(f?"[^"]*执行[^"]*"\)',
    r'(\s+)print\(f?"[^"]*切换[^"]*"\)',
    r'(\s+)print\(f?"[^"]*选择[^"]*"\)',
    r'(\s+)print\(f?"[^"]*添加[^"]*"\)',
    r'(\s+)print\(f?"[^"]*删除[^"]*"\)',
    r'(\s+)print\(f?"[^"]*清空[^"]*"\)',
    r'(\s+)print\(f?"[^"]*重置[^"]*"\)',
    r'(\s+)print\(f?"[^"]*恢复[^"]*"\)',
    r'(\s+)print\(f?"[^"]*修改[^"]*"\)',
    r'(\s+)print\(f?"[^"]*变更[^"]*"\)',
    r'(\s+)print\(f?"[^"]*调整[^"]*"\)',
    r'(\s+)print\(f?"[^"]*配置[^"]*"\)',
    r'(\s+)print\(f?"[^"]*设定[^"]*"\)',
    r'(\s+)print\(f?"[^"]*建立[^"]*"\)',
    r'(\s+)print\(f?"[^"]*关闭[^"]*"\)',
    r'(\s+)print\(f?"[^"]*结束[^"]*"\)',
    r'(\s+)print\(f?"[^"]*退出[^"]*"\)',
    r'(\s+)print\(f?"[^"]*进入[^"]*"\)',
    r'(\s+)print\(f?"[^"]*离开[^"]*"\)',
    r'(\s+)print\(f?"[^"]*返回[^"]*"\)',
    r'(\s+)print\(f?"[^"]*跳过[^"]*"\)',
    r'(\s+)print\(f?"[^"]*忽略[^"]*"\)',
    r'(\s+)print\(f?"[^"]*继续[^"]*"\)',
    r'(\s+)print\(f?"[^"]*暂停[^"]*"\)',
    r'(\s+)print\(f?"[^"]*重试[^"]*"\)',
    r'(\s+)print\(f?"[^"]*等待[^"]*"\)',
    r'(\s+)print\(f?"[^"]*监听[^"]*"\)',
    r'(\s+)print\(f?"[^"]*监控[^"]*"\)',
    r'(\s+)print\(f?"[^"]*检查[^"]*"\)',
    r'(\s+)print\(f?"[^"]*验证[^"]*"\)',
    r'(\s+)print\(f?"[^"]*确认[^"]*"\)',
    r'(\s+)print\(f?"[^"]*测试[^"]*"\)',
    r'(\s+)print\(f?"[^"]*调试[^"]*"\)',
    r'(\s+)print\(f?"[^"]*日志[^"]*"\)',
    r'(\s+)print\(f?"[^"]*信息[^"]*"\)',
    r'(\s+)print\(f?"[^"]*状态[^"]*"\)',
    r'(\s+)print\(f?"[^"]*结果[^"]*"\)',
    r'(\s+)print\(f?"[^"]*数据[^"]*"\)',
    r'(\s+)print\(f?"[^"]*参数[^"]*"\)',
    r'(\s+)print\(f?"[^"]*选项[^"]*"\)',
    r'(\s+)print\(f?"[^"]*设备[^"]*"\)',
    r'(\s+)print\(f?"[^"]*文件[^"]*"\)',
    r'(\s+)print\(f?"[^"]*路径[^"]*"\)',
    r'(\s+)print\(f?"[^"]*目录[^"]*"\)',
    r'(\s+)print\(f?"[^"]*窗口[^"]*"\)',
    r'(\s+)print\(f?"[^"]*界面[^"]*"\)',
    r'(\s+)print\(f?"[^"]*组件[^"]*"\)',
    r'(\s+)print\(f?"[^"]*控件[^"]*"\)',
    r'(\s+)print\(f?"[^"]*按钮[^"]*"\)',
    r'(\s+)print\(f?"[^"]*标签[^"]*"\)',
    r'(\s+)print\(f?"[^"]*输入[^"]*"\)',
    r'(\s+)print\(f?"[^"]*输出[^"]*"\)',
    r'(\s+)print\(f?"[^"]*播放[^"]*"\)',
    r'(\s+)print\(f?"[^"]*音频[^"]*"\)',
    r'(\s+)print\(f?"[^"]*视频[^"]*"\)',
    r'(\s+)print\(f?"[^"]*媒体[^"]*"\)',
    r'(\s+)print\(f?"[^"]*源[^"]*"\)',
    r'(\s+)print\(f?"[^"]*流[^"]*"\)',
    r'(\s+)print\(f?"[^"]*数据流[^"]*"\)',
    r'(\s+)print\(f?"[^"]*信号[^"]*"\)',
    r'(\s+)print\(f?"[^"]*事件[^"]*"\)',
    r'(\s+)print\(f?"[^"]*消息[^"]*"\)',
    r'(\s+)print\(f?"[^"]*通知[^"]*"\)',
    r'(\s+)print\(f?"[^"]*提示[^"]*"\)',
]

# 逐个应用模式
for pattern in patterns:
    content = re.sub(pattern, r'\1# print(', content, flags=re.MULTILINE)

# 写回文件
with open('main_module.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("调试日志注释完成！")
