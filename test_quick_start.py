#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_quick_start():
    """测试一键启动功能"""
    try:
        print("开始测试一键启动功能...")
        
        # 导入必要的模块
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入主模块
        import main_module
        
        # 创建主窗口
        main_window = main_module.MainWindow()
        
        print("主窗口创建成功")
        
        # 测试一键启动对话框创建
        try:
            dialog = main_module.QuickStartDialog(main_window)
            print("一键启动对话框创建成功")
            dialog.close()
        except Exception as e:
            print(f"创建对话框时出错: {e}")
            traceback.print_exc()
        
        # 测试功能启动方法
        try:
            # 模拟选择媒体源
            main_window.video_source_combo.addItem("测试视频源", "test_video")
            main_window.video_source_combo.setCurrentIndex(0)
            main_window.audio_source_combo.addItem("测试音频源", "test_audio")
            main_window.audio_source_combo.setCurrentIndex(0)
            
            # 模拟连接状态
            main_window.is_connected = True
            
            # 测试单个功能启动
            result = main_window.start_single_function("speed")
            print(f"测试启动speed功能: {result}")
            
        except Exception as e:
            print(f"测试功能启动时出错: {e}")
            traceback.print_exc()
        
        print("测试完成")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_quick_start()
    if success:
        print("✅ 测试通过")
        sys.exit(0)
    else:
        print("❌ 测试失败")
        sys.exit(1)
